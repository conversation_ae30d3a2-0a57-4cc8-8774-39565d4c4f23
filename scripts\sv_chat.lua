RegisterServerEvent('chatt:init')
RegisterServerEvent('chatt:addTemplate')
RegisterServerEvent('chatt:addMessage')
RegisterServerEvent('chatt:addSuggestion')
RegisterServerEvent('chatt:removeSuggestion')
RegisterServerEvent('_chatt:messageEntered')
RegisterServerEvent('chatt:clear')
RegisterServerEvent('__cfx_internal:commandFallback')


AddEventHandler('__cfx_internal:commandFallback', function(command)
    local name = GetPlayerName(source)

    TriggerEvent('chatMessage', source, name, '/' .. command) -- compat: also emit legacy chatMessage

    if not WasEventCanceled() then
        TriggerClientEvent('chatMessage', -1, name, { 255, 255, 255 }, '/' .. command) 
    end

    CancelEvent()
end)

-- command suggestions for clients
local function refreshCommands(player)
    if not GetRegisteredCommands then return end

    local ok, registeredCommands = pcall(GetRegisteredCommands)
    if not ok or type(registeredCommands) ~= 'table' then return end

    local suggestions = {}

    for _, command in ipairs(registeredCommands) do
        if command and command.name and command.name ~= 'toggleChat' then
            local firstChar = command.name:sub(1,1)
            if firstChar ~= '+' and firstChar ~= '-' and firstChar ~= '_' then
                table.insert(suggestions, { name='/'..command.name, help='' })
            end
        end
    end

    TriggerClientEvent('chatt:addSuggestions', player, suggestions)
end

AddEventHandler('core_chatt:init', function()
    refreshCommands(source)
end)

AddEventHandler('onServerResourceStart', function(resName)
    Wait(500)

    for _, player in ipairs(GetPlayers()) do
        refreshCommands(player)
    end
end)
