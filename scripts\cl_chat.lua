-- Single-file standalone core_chat server + client in one
-- Keeps existing HTML/CSS/JS; only <PERSON><PERSON> consolidated here.

if IsDuplicityVersion() then
  -- SERVER SIDE ---------------------------------------------------------------

  RegisterNetEvent('chatt:init')
  AddEventHandler('chatt:init', function() end)

  -- Relay messages entered from clients to everyone
  RegisterNetEvent('_chatt:messageEntered')
  AddEventHandler('_chatt:messageEntered', function(author, color, message, mode)
    if not message or message == '' then return end
    color = color or {255, 255, 255}

    -- Legacy event consumers
    TriggerClientEvent('chatMessage', -1, author or '', color, message)

    -- UI message for all clients
    local args = (author and author ~= '') and {author, message} or {message}
    TriggerClientEvent('chatt:addMessage', -1, {
      color = color,
      multiline = true,
      args = args
    })
  end)

  return
end

-- CLIENT SIDE -----------------------------------------------------------------

local isRDR = not TerraingridActivate and true or false

local chatInputActive = false
local chatInputActivating = false
local chatLoaded = false

RegisterNetEvent('chatMessage')
RegisterNetEvent('chatt:addTemplate')
RegisterNetEvent('chatt:addMessage')
RegisterNetEvent('chatt:addSuggestion')
RegisterNetEvent('chatt:addSuggestions')
RegisterNetEvent('chatt:addMode')
RegisterNetEvent('chatt:removeMode')
RegisterNetEvent('chatt:removeSuggestion')
RegisterNetEvent('chatt:clear')

-- internal events
RegisterNetEvent('__cfx_internal:serverPrint')

RegisterNetEvent('_chatt:messageEntered')

-- deprecated, use chatt:addMessage
AddEventHandler('chatMessage', function(author, color, text)
  local args = { text }
  if author ~= "" then
    table.insert(args, 1, author)
  end
  SendNUIMessage({
    type = 'ON_MESSAGE',
    message = {
      color = color,
      multiline = true,
      args = args
    }
  })
end)

AddEventHandler('__cfx_internal:serverPrint', function(msg)
  print(msg)

  SendNUIMessage({
    type = 'ON_MESSAGE',
    message = {
      templateId = 'print',
      multiline = true,
      args = { msg },
      mode = '_global'
    }
  })
end)

-- addMessage
local addMessage = function(message)
  if type(message) == 'string' then
    message = {
      args = { message }
    }
  end

  SendNUIMessage({
    type = 'ON_MESSAGE',
    message = message
  })
end

exports('addMessage', addMessage)
AddEventHandler('chatt:addMessage', addMessage)

-- addSuggestion
local addSuggestion = function(name, help, params)
  SendNUIMessage({
    type = 'ON_SUGGESTION_ADD',
    suggestion = {
      name = name,
      help = help,
      params = params or nil
    }
  })
end

exports('addSuggestion', addSuggestion)
AddEventHandler('chatt:addSuggestion', addSuggestion)

AddEventHandler('chatt:addSuggestions', function(suggestions)
  for _, suggestion in ipairs(suggestions) do
    SendNUIMessage({
      type = 'ON_SUGGESTION_ADD',
      suggestion = suggestion
    })
  end
end)

AddEventHandler('chatt:removeSuggestion', function(name)
  SendNUIMessage({
    type = 'ON_SUGGESTION_REMOVE',
    name = name
  })
end)

AddEventHandler('chatt:addMode', function(mode)
  SendNUIMessage({
    type = 'ON_MODE_ADD',
    mode = mode
  })
end)

AddEventHandler('chatt:removeMode', function(name)
  SendNUIMessage({
    type = 'ON_MODE_REMOVE',
    name = name
  })
end)

AddEventHandler('chatt:addTemplate', function(id, html)
  SendNUIMessage({
    type = 'ON_TEMPLATE_ADD',
    template = {
      id = id,
      html = html
    }
  })
end)

AddEventHandler('chatt:clear', function(name)
  SendNUIMessage({
    type = 'ON_CLEAR'
  })
end)

RegisterNUICallback('chatResult', function(data, cb)
  chatInputActive = false
  SetNuiFocus(false)

  if not data.canceled then
    local id = PlayerId()

    --deprecated
    local r, g, b = 0, 0x99, 255

    if data.message:sub(1, 1) == '/' then
      ExecuteCommand(data.message:sub(2))
    else
      TriggerServerEvent('_chatt:messageEntered', GetPlayerName(id), { r, g, b }, data.message, data.mode)
    end
  end

  cb('ok')
end)

-- Track current suggestion set to avoid duplicates and stale entries
local __chat_suggestion_set = {}

local function refreshCommands()
  if not GetRegisteredCommands then return end

  local ok, registeredCommands = pcall(GetRegisteredCommands)
  if not ok or type(registeredCommands) ~= 'table' then return end

  local suggestions = {}
  local newSet = {}

  for _, command in ipairs(registeredCommands) do
    if command and command.name and command.name ~= 'toggleChat' then
      local firstChar = command.name:sub(1,1)
      -- skip some internal aliases like +, -, _
      if firstChar ~= '+' and firstChar ~= '-' and firstChar ~= '_' then
        local name = '/' .. command.name
        newSet[name] = true
        if not __chat_suggestion_set[name] then
          local s = { name = name, help = '' }
          table.insert(suggestions, s)
          -- fire per-suggestion as well to maximize compatibility
          TriggerEvent('chatt:addSuggestion', s.name, s.help or '', s.params or {})
        end
      end
    end
  end

  if #suggestions > 0 then
    -- batch for UIs that listen for bulk event
    TriggerEvent('chatt:addSuggestions', suggestions)
  end

  -- remove stale suggestions that no longer exist
  for name, _ in pairs(__chat_suggestion_set) do
    if not newSet[name] then
      TriggerEvent('chatt:removeSuggestion', name)
    end
  end

  __chat_suggestion_set = newSet
end

local function refreshThemes()
  local themes = {}

  for resIdx = 0, GetNumResources() - 1 do
    local resource = GetResourceByFindIndex(resIdx)

    if GetResourceState(resource) == 'started' then
      local numThemes = GetNumResourceMetadata(resource, 'chat_theme')

      if numThemes > 0 then
        local themeName = GetResourceMetadata(resource, 'chat_theme')
        local themeData = json.decode(GetResourceMetadata(resource, 'chat_theme_extra') or 'null')

        if themeName and themeData then
          themeData.baseUrl = 'nui://' .. resource .. '/'
          themes[themeName] = themeData
        end
      end
    end
  end

  SendNUIMessage({
    type = 'ON_UPDATE_THEMES',
    themes = themes
  })
end

AddEventHandler('onClientResourceStart', function(resName)
  Wait(500)
  refreshCommands()
  refreshThemes()
end)

AddEventHandler('onClientResourceStop', function(resName)
  Wait(500)
  refreshCommands()
  refreshThemes()
end)

RegisterNUICallback('loaded', function(data, cb)
  TriggerServerEvent('chatt:init')
  refreshCommands()
  refreshThemes()
  chatLoaded = true
  cb('ok')
end)

local CHAT_HIDE_STATES = {
  SHOW_WHEN_ACTIVE = 0,
  ALWAYS_SHOW = 1,
  ALWAYS_HIDE = 2
}

-- local kvpEntry = GetResourceKvpString('hideState')
local chatHideState = CHAT_HIDE_STATES.SHOW_WHEN_ACTIVE
local isFirstHide = true

CreateThread(function()
  SetTextChatEnabled(false)
  SetNuiFocus(false)

  local lastChatHideState = -1
  local origChatHideState = -1

  while true do
    Wait(0)

    if not chatInputActive then
      if IsControlPressed(0, isRDR and `INPUT_MP_TEXT_CHAT_ALL` or 245) then
        chatInputActive = true
        chatInputActivating = true

        -- refresh suggestions right before opening the input
        refreshCommands()
        SendNUIMessage({ type = 'ON_OPEN' })
      end
    end

    if chatInputActivating then
      if not IsControlPressed(0, isRDR and `INPUT_MP_TEXT_CHAT_ALL` or 245) then
        SetNuiFocus(true, true) -- explicit to avoid autofocus blocked warnings
        chatInputActivating = false
      end
    end

    if chatLoaded then
      local forceHide = IsScreenFadedOut() or IsPauseMenuActive()
      local wasForceHide = false

      if chatHideState ~= CHAT_HIDE_STATES.ALWAYS_HIDE then
        if forceHide then
          origChatHideState = chatHideState
          chatHideState = CHAT_HIDE_STATES.ALWAYS_HIDE
        end
      elseif not forceHide and origChatHideState ~= -1 then
        chatHideState = origChatHideState
        origChatHideState = -1
        wasForceHide = true
      end

      if chatHideState ~= lastChatHideState then
        lastChatHideState = chatHideState
        SendNUIMessage({
          type = 'ON_SCREEN_STATE_CHANGE',
          hideState = chatHideState,
          fromUserInteraction = not forceHide and not isFirstHide and not wasForceHide
        })
        isFirstHide = false
      end
    end
  end
end)

local function getTimestamp()
  local meridiem = 'AM'
  year , month , day , hour , minute , second = ''
  if GetGameName() == 'fivem' then
      year , month , day , hour , minute , second = GetLocalTime()
  elseif GetGameName() == 'redm' then
      year , month , day , hour , minute , second = GetPosixTime()
  end
  if hour >= 13 then
      hour = hour - 12
      meridiem = 'PM'
  end
  if hour == 12 then
      meridiem = 'PM'
  end
  if minute <= 9 then
      minute = '0' .. minute
  end
  timestamp = hour .. ':' .. minute .. ' ' .. meridiem
  return timestamp
end

exports('getTimestamp', getTimestamp)

